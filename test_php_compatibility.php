<?php
// 测试PHP兼容性的简单脚本
// 检查是否存在putenv函数
echo "PHP Version: " . PHP_VERSION . "\n";
echo "putenv function exists: " . (function_exists('putenv') ? 'Yes' : 'No') . "\n";

// 测试基本的ThinkPHP加载
try {
    // 定义应用目录
    define('APP_PATH', __DIR__ . '/application/');
    
    // 加载框架引导文件
    require __DIR__ . '/thinkphp/base.php';
    
    echo "ThinkPHP base.php loaded successfully!\n";
    
} catch (Exception $e) {
    echo "Error loading ThinkPHP: " . $e->getMessage() . "\n";
} catch (Error $e) {
    echo "Fatal Error: " . $e->getMessage() . "\n";
}
