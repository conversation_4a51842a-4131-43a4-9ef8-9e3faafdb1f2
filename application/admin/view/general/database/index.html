<style type="text/css">
    #searchfloat {position:absolute;top:40px;right:20px;background:#F7F0A0;padding:10px;}
    #saved {position: relative;}
    #saved_sql {position:absolute;bottom:0;height:300px;background:#F7F0A0;width:100%;overflow:auto;display:none;}
    #saved_sql li {display:block;clear:both;width:100%;float:left;line-height:18px;padding:1px 0}
    #saved_sql li a{float:left;text-decoration: none;display:block;padding:0 5px;}
    #saved_sql li i{display:none;float:left;color:#06f;font-size: 14px;font-style: normal;margin-left:2px;line-height:18px;}
    #saved_sql li:hover{background:#fff;}
    #saved_sql li:hover i{display:block;cursor:pointer;}
    #database #tablename {height:205px;width:100%;padding:5px;}
    #database #tablename option{height:18px;}
    #database #subaction {height:210px;width:100%;}
    #database .select-striped > option:nth-of-type(odd) {background-color: #f9f9f9;}
    #database .dropdown-menu ul {margin:-3px 0;}
    #database .dropdown-menu ul li{margin:3px 0;}
    #database .dropdown-menu.row .col-xs-6{padding:0 5px;}
    #sqlquery {color:#444;}
    #resultparent {padding:5px; height:calc(100vh - 350px);}
</style>
<style data-render="darktheme">
    body.darktheme #database .select-striped > option:nth-of-type(odd) {
        background-color: #262626;
    }
    body.darktheme #tablename::-webkit-scrollbar {
        width: 9px;
        height: 9px;
    }
    body.darktheme #tablename::-webkit-scrollbar-thumb {
        background: #999;
    }
    body.darktheme #tablename::-webkit-scrollbar-track {
        background: #333;
    }
    body.darktheme #sqlquery {
        color: #ccc;
    }
</style>
<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="database" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    {if $auth->check('general/database/query')}
                    <div class="row">
                        <div class="col-xs-4">
                            <h4>{:__('SQL Result')}:</h4>
                        </div>
                        <div class="col-xs-8 text-right">
                            <form action="{:url('general.database/query')}" method="post" name="infoform" target="resultframe">
                                <input type="hidden" name="do_action" id="topaction" />
                                <div class="btn-group">
                                    <button data-toggle="dropdown" class="btn btn-primary btn-embossed dropdown-toggle" type="button">{:__('Basic query')} <span class="caret"></span></button>
                                    <div class="row dropdown-menu pull-right" style="width:300px;">
                                        <div class="col-xs-6">
                                            <select class="form-control select-striped" id="tablename" name="tablename[]" multiple="multiple">
                                                {foreach $tables as $table}
                                                <option value="{$table.name}" title="">{$table.name}<!--({$table.rows})--></option>
                                                {/foreach}
                                            </select>
                                        </div>
                                        <div class="col-xs-6">
                                            <ul id="subaction" class="list-unstyled">
                                                <li><input type="submit" name="submit1" value="{:__('View structure')}" rel="viewinfo" class="btn btn-primary btn-embossed btn-sm btn-block"/></li>
                                                <li><input type="submit" name="submit2" value="{:__('View data')}" rel="viewdata" class="btn btn-primary btn-embossed btn-sm btn-block"/></li>
                                                <li><input type="submit" name="submit3" value="{:__('Optimize')}" rel="optimize" class="btn btn-primary btn-embossed btn-sm btn-block" /></li>
                                                <li><input type="submit" name="submit4" value="{:__('Repair')}" rel="repair" class="btn btn-primary btn-embossed btn-sm btn-block"/></li>
                                                <li><input type="submit" name="submit5" value="{:__('Optimize all')}" rel="optimizeall" class="btn btn-primary btn-embossed btn-sm btn-block" /></li>
                                                <li><input type="submit" name="submit6" value="{:__('Repair all')}" rel="repairall" class="btn btn-primary btn-embossed btn-sm btn-block" /></li>
                                            </ul>
                                        </div>
                                        <div class="clear"></div>
                                    </div>

                                </div>
                            </form>
                        </div>

                    </div>
                    <div class="well" id="resultparent">
                        <iframe name="resultframe" frameborder="0" id="resultframe" style="height:100%;" width="100%" height="100%"></iframe>
                    </div>
                    <form action="{:url('general.database/query')}" method="post" id="sqlexecute" name="form1" target="resultframe">
                        <input type="hidden" name="do_action" value="doquery" />
                        <div class="form-group">
                            <textarea name="sqlquery" placeholder="{:__('Executes one or multiple queries which are concatenated by a semicolon')}" cols="60" rows="5" class="form-control" id="sqlquery"></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary btn-embossed"><i class="fa fa-check"></i> {:__('Execute')}</button>
                        <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                    </form>
                    {else /}
                    <div id="backuplist"></div>
                    {/if}
                </div>
            </div>

        </div>
    </div>
</div>
