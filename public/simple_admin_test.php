<?php
// 简单的admin测试

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');

// 判断是否安装
if (!is_file(APP_PATH . 'admin/command/Install/install.lock')) {
    echo "安装锁文件不存在";
    exit;
}

try {
    // 加载框架引导文件
    require __DIR__ . '/../thinkphp/base.php';

    // 绑定到admin模块
    \think\Route::bind('admin');

    // 关闭路由
    \think\App::route(false);

    // 设置根url
    \think\Url::root('');

    // 清理所有可能导致重定向的参数
    unset($_GET['ref']);
    unset($_GET['addtabs']);
    unset($_GET['dialog']);
    
    // 设置为正常的GET请求
    $_SERVER['REQUEST_METHOD'] = 'GET';
    $_GET['s'] = 'index/index';

    echo "=== 简化测试 ===\n";
    echo "清理后的GET参数: " . print_r($_GET, true) . "\n";

    // 执行应用
    $response = \think\App::run();
    
    echo "响应类型: " . get_class($response) . "\n";
    
    if ($response instanceof \think\response\Redirect) {
        echo "仍然检测到重定向\n";
        
        // 获取重定向的详细信息
        $reflection = new ReflectionClass($response);
        $properties = $reflection->getProperties();
        
        foreach ($properties as $property) {
            $property->setAccessible(true);
            $value = $property->getValue($response);
            if (!empty($value)) {
                echo $property->getName() . ": " . print_r($value, true) . "\n";
            }
        }
    } else {
        echo "正常响应，开始输出内容\n";
        $response->send();
    }

} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}
?>
