<?php
// 服务器环境诊断

echo "=== 服务器环境信息 ===\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "服务器软件: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "文档根目录: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
echo "脚本路径: " . $_SERVER['SCRIPT_FILENAME'] . "\n";
echo "\n";

echo "=== URL相关信息 ===\n";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'Not set') . "\n";
echo "SCRIPT_NAME: " . ($_SERVER['SCRIPT_NAME'] ?? 'Not set') . "\n";
echo "PATH_INFO: " . ($_SERVER['PATH_INFO'] ?? 'Not set') . "\n";
echo "QUERY_STRING: " . ($_SERVER['QUERY_STRING'] ?? 'Not set') . "\n";
echo "REQUEST_METHOD: " . ($_SERVER['REQUEST_METHOD'] ?? 'Not set') . "\n";
echo "\n";

echo "=== FastCGI相关 ===\n";
echo "SCRIPT_FILENAME: " . ($_SERVER['SCRIPT_FILENAME'] ?? 'Not set') . "\n";
echo "REDIRECT_STATUS: " . ($_SERVER['REDIRECT_STATUS'] ?? 'Not set') . "\n";
echo "GATEWAY_INTERFACE: " . ($_SERVER['GATEWAY_INTERFACE'] ?? 'Not set') . "\n";
echo "\n";

echo "=== 文件权限检查 ===\n";
$files = [
    __DIR__ . '/tandelin.php',
    __DIR__ . '/../application',
    __DIR__ . '/../runtime',
    __DIR__ . '/../thinkphp'
];

foreach ($files as $file) {
    if (file_exists($file)) {
        $perms = substr(sprintf('%o', fileperms($file)), -4);
        echo basename($file) . ": $perms " . (is_readable($file) ? '可读' : '不可读') . " " . (is_writable($file) ? '可写' : '不可写') . "\n";
    } else {
        echo basename($file) . ": 不存在\n";
    }
}
echo "\n";

echo "=== 测试直接访问 ===\n";
echo "当前脚本: " . $_SERVER['REQUEST_URI'] . "\n";
echo "测试链接:\n";
echo "1. <a href='tandelin.php'>直接访问tandelin.php</a>\n";
echo "2. <a href='tandelin.php?s=index/index'>使用查询参数</a>\n";
echo "3. <a href='tandelin.php/index/index'>使用路径信息</a>\n";
echo "\n";

echo "=== PHP配置 ===\n";
echo "cgi.fix_pathinfo: " . ini_get('cgi.fix_pathinfo') . "\n";
echo "enable_dl: " . ini_get('enable_dl') . "\n";
echo "max_execution_time: " . ini_get('max_execution_time') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";
?>
