<?php
// 测试版本的tandelin.php

echo "=== 开始测试 ===\n";

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');
echo "APP_PATH定义完成\n";

// 判断是否安装
if (!is_file(APP_PATH . 'admin/command/Install/install.lock')) {
    echo "安装锁文件不存在，跳转到安装页面\n";
    header("location:./install.php");
    exit;
}
echo "安装锁文件检查通过\n";

try {
    // 加载框架引导文件
    echo "开始加载base.php\n";
    require __DIR__ . '/../thinkphp/base.php';
    echo "base.php加载成功\n";

    // 绑定到admin模块
    echo "开始绑定admin模块\n";
    \think\Route::bind('admin');
    echo "admin模块绑定成功\n";

    // 关闭路由
    echo "开始关闭路由\n";
    \think\App::route(false);
    echo "路由关闭成功\n";

    // 设置根url
    echo "开始设置根URL\n";
    \think\Url::root('');
    echo "根URL设置成功\n";

    // 执行应用
    echo "开始执行应用\n";
    $response = \think\App::run();
    echo "应用执行成功，响应类型: " . get_class($response) . "\n";
    
    // 发送响应
    echo "开始发送响应\n";
    $response->send();
    echo "响应发送完成\n";

} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
} catch (Throwable $e) {
    echo "Throwable: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}
?>
