<!DOCTYPE html>
<html>
<head>
    <title>登录测试</title>
    <meta charset="utf-8">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h2>登录测试</h2>
    <form id="loginForm">
        <div>
            <label>用户名:</label>
            <input type="text" id="username" name="username" value="admin">
        </div>
        <div>
            <label>密码:</label>
            <input type="password" id="password" name="password" value="">
        </div>
        <div>
            <button type="submit">登录</button>
        </div>
    </form>
    
    <div id="result"></div>

    <script>
    $(document).ready(function() {
        $('#loginForm').on('submit', function(e) {
            e.preventDefault();
            
            var username = $('#username').val();
            var password = $('#password').val();
            
            // 先获取token
            $.get('tandelin.php?s=index/login', function(html) {
                // 从HTML中提取token
                var tokenMatch = html.match(/name="__token__"\s+value="([^"]+)"/);
                var token = tokenMatch ? tokenMatch[1] : '';
                
                console.log('Token:', token);
                
                // 发送登录请求
                $.ajax({
                    url: 'tandelin.php?s=index/login',
                    type: 'POST',
                    dataType: 'json',
                    data: {
                        username: username,
                        password: password,
                        __token__: token
                    },
                    success: function(response) {
                        console.log('Success:', response);
                        $('#result').html('<div style="color: green;">登录成功: ' + JSON.stringify(response) + '</div>');
                    },
                    error: function(xhr, status, error) {
                        console.log('Error:', xhr.responseText);
                        console.log('Status:', status);
                        console.log('Error:', error);
                        $('#result').html('<div style="color: red;">登录失败: ' + xhr.responseText + '</div>');
                    }
                });
            });
        });
    });
    </script>
</body>
</html>
