<?php
// 直接测试admin首页

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');

// 判断是否安装
if (!is_file(APP_PATH . 'admin/command/Install/install.lock')) {
    echo "安装锁文件不存在";
    exit;
}

try {
    // 加载框架引导文件
    require __DIR__ . '/../thinkphp/base.php';

    // 绑定到admin模块
    \think\Route::bind('admin');

    // 关闭路由
    \think\App::route(false);

    // 设置根url
    \think\Url::root('');

    // 模拟访问首页
    $_GET['s'] = 'index/index';
    $_SERVER['PATH_INFO'] = '/index/index';
    $_SERVER['REQUEST_URI'] = '/admin_index_test.php/index/index';

    echo "=== 模拟访问admin首页 ===\n";
    echo "PATH_INFO: " . ($_SERVER['PATH_INFO'] ?? 'Not set') . "\n";
    echo "REQUEST_URI: " . $_SERVER['REQUEST_URI'] . "\n";
    echo "GET参数: " . print_r($_GET, true) . "\n";

    // 检查可能导致重定向的条件
    echo "\n=== 重定向条件检查 ===\n";
    echo "REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD'] . "\n";
    echo "isPost: " . ($_SERVER['REQUEST_METHOD'] === 'POST' ? 'true' : 'false') . "\n";
    echo "addtabs参数: " . ($_GET['addtabs'] ?? 'Not set') . "\n";
    echo "dialog参数: " . ($_GET['dialog'] ?? 'Not set') . "\n";
    echo "ref参数: " . ($_GET['ref'] ?? 'Not set') . "\n";

    // 执行应用
    $response = \think\App::run();
    
    echo "响应类型: " . get_class($response) . "\n";
    
    if ($response instanceof \think\response\Redirect) {
        echo "检测到重定向\n";
        $headers = $response->getHeader();
        echo "重定向目标: " . ($headers['Location'] ?? '未知') . "\n";
        echo "所有响应头: " . print_r($headers, true) . "\n";

        // 尝试获取重定向URL的详细信息
        try {
            $reflection = new ReflectionClass($response);
            $urlProperty = $reflection->getProperty('url');
            $urlProperty->setAccessible(true);
            $url = $urlProperty->getValue($response);
            echo "内部URL: " . $url . "\n";
        } catch (Exception $e) {
            echo "无法获取内部URL: " . $e->getMessage() . "\n";
        }
    } else {
        echo "正常响应\n";
        // 发送响应
        $response->send();
    }

} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}
?>
