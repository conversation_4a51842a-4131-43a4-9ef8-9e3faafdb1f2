<?php
// 数据库连接测试

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');

try {
    // 加载框架引导文件
    require __DIR__ . '/../thinkphp/base.php';

    echo "=== 数据库连接测试 ===\n";
    
    // 获取数据库配置
    $config = include APP_PATH . 'database.php';
    
    echo "数据库配置:\n";
    echo "  hostname: " . $config['hostname'] . "\n";
    echo "  database: " . $config['database'] . "\n";
    echo "  username: " . $config['username'] . "\n";
    echo "  password: " . ($config['password'] ? '***已设置***' : '空') . "\n";
    echo "  hostport: " . $config['hostport'] . "\n";
    echo "\n";

    // 测试原生PDO连接
    echo "=== 原生PDO连接测试 ===\n";
    $dsn = "mysql:host={$config['hostname']};port={$config['hostport']};dbname={$config['database']};charset={$config['charset']}";
    echo "DSN: $dsn\n";
    
    try {
        $pdo = new PDO($dsn, $config['username'], $config['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo "PDO连接成功！\n";
        
        // 测试查询
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        echo "测试查询结果: " . $result['test'] . "\n";
        
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE '{$config['prefix']}admin'");
        $table = $stmt->fetch();
        if ($table) {
            echo "admin表存在\n";
            
            // 检查admin用户数量
            $stmt = $pdo->query("SELECT COUNT(*) as count FROM {$config['prefix']}admin");
            $count = $stmt->fetch();
            echo "admin用户数量: " . $count['count'] . "\n";
        } else {
            echo "admin表不存在\n";
        }
        
    } catch (PDOException $e) {
        echo "PDO连接失败: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== ThinkPHP数据库连接测试 ===\n";
    try {
        // 使用ThinkPHP的数据库连接
        $db = \think\Db::connect($config);
        $result = $db->query("SELECT 1 as test");
        echo "ThinkPHP连接成功！\n";
        echo "测试查询结果: " . $result[0]['test'] . "\n";
        
    } catch (Exception $e) {
        echo "ThinkPHP连接失败: " . $e->getMessage() . "\n";
    }

} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}
?>
