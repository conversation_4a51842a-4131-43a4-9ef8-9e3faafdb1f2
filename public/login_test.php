<?php
// 直接测试登录页面

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');

// 判断是否安装
if (!is_file(APP_PATH . 'admin/command/Install/install.lock')) {
    echo "安装锁文件不存在";
    exit;
}

try {
    // 加载框架引导文件
    require __DIR__ . '/../thinkphp/base.php';

    // 绑定到admin模块
    \think\Route::bind('admin');

    // 关闭路由
    \think\App::route(false);

    // 设置根url
    \think\Url::root('');

    // 模拟访问登录页面
    $_GET['s'] = 'index/login';
    $_SERVER['PATH_INFO'] = '/index/login';
    $_SERVER['REQUEST_URI'] = '/index/login';

    // 执行应用
    $response = \think\App::run();
    
    // 发送响应
    $response->send();

} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}
?>
