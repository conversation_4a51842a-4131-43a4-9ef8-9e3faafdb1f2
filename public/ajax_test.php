<?php
// AJAX登录测试脚本

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');

// 判断是否安装
if (!is_file(APP_PATH . 'admin/command/Install/install.lock')) {
    echo json_encode(['error' => '安装锁文件不存在']);
    exit;
}

try {
    // 加载框架引导文件
    require __DIR__ . '/../thinkphp/base.php';

    // 绑定到admin模块
    \think\Route::bind('admin');

    // 关闭路由
    \think\App::route(false);

    // 设置根url
    \think\Url::root('');

    // 模拟AJAX POST请求到登录接口
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['HTTP_X_REQUESTED_WITH'] = 'XMLHttpRequest';
    $_SERVER['CONTENT_TYPE'] = 'application/x-www-form-urlencoded';
    
    // 设置POST数据
    $_POST['username'] = 'admin';  // 请替换为实际的用户名
    $_POST['password'] = 'admin';  // 请替换为实际的密码
    $_POST['__token__'] = '';      // 这里需要实际的token
    
    // 设置路径信息
    $_GET['s'] = 'index/login';
    $_SERVER['PATH_INFO'] = '/index/login';
    $_SERVER['REQUEST_URI'] = '/index/login';

    echo "=== 请求信息 ===\n";
    echo "REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD'] . "\n";
    echo "HTTP_X_REQUESTED_WITH: " . ($_SERVER['HTTP_X_REQUESTED_WITH'] ?? 'Not set') . "\n";
    echo "isAjax: " . (\think\Request::instance()->isAjax() ? 'true' : 'false') . "\n";
    echo "default_ajax_return: " . \think\Config::get('default_ajax_return') . "\n";
    echo "default_return_type: " . \think\Config::get('default_return_type') . "\n";
    echo "\n=== 执行应用 ===\n";

    // 执行应用
    $response = \think\App::run();
    
    echo "响应类型: " . get_class($response) . "\n";
    echo "响应内容类型: " . $response->getHeader('Content-Type') . "\n";
    
    // 获取响应内容
    $content = $response->getContent();
    echo "响应内容: " . $content . "\n";
    
    // 检查是否是有效的JSON
    $json = json_decode($content, true);
    if (json_last_error() === JSON_ERROR_NONE) {
        echo "JSON解析成功\n";
        print_r($json);
    } else {
        echo "JSON解析失败: " . json_last_error_msg() . "\n";
    }

} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}
?>
