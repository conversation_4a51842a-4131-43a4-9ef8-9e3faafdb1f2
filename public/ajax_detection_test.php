<?php
// 测试AJAX检测

header('Content-Type: application/json; charset=utf-8');

echo json_encode([
    'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? 'Not set',
    'HTTP_X_REQUESTED_WITH' => $_SERVER['HTTP_X_REQUESTED_WITH'] ?? 'Not set',
    'CONTENT_TYPE' => $_SERVER['CONTENT_TYPE'] ?? 'Not set',
    'HTTP_ACCEPT' => $_SERVER['HTTP_ACCEPT'] ?? 'Not set',
    'POST_data' => $_POST,
    'GET_data' => $_GET,
    'is_ajax_by_header' => isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest',
    'php_version' => PHP_VERSION,
    'current_time' => date('Y-m-d H:i:s')
], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
?>
