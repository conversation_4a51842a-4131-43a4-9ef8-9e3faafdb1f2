/*
 tableExport.jquery.plugin

 Version 1.33.0

 Copyright (c) 2015-2025 hhurz,
   https://github.com/hhurz/tableExport.jquery.plugin

 Based on https://github.com/kayalshri/tableExport.jquery.plugin

 Licensed under the MIT License
*/
var $jscomp=$jscomp||{};$jscomp.scope={};$jscomp.findInternal=function(d,H,u){d instanceof String&&(d=String(d));for(var D=d.length,K=0;K<D;K++){var xa=d[K];if(H.call(u,xa,K,d))return{i:K,v:xa}}return{i:-1,v:void 0}};$jscomp.ASSUME_ES5=!1;$jscomp.ASSUME_NO_NATIVE_MAP=!1;$jscomp.ASSUME_NO_NATIVE_SET=!1;$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(d,H,u){d!=Array.prototype&&d!=Object.prototype&&(d[H]=u.value)};
$jscomp.getGlobal=function(d){return"undefined"!=typeof window&&window===d?d:"undefined"!=typeof global&&null!=global?global:d};$jscomp.global=$jscomp.getGlobal(this);$jscomp.polyfill=function(d,H,u,D){if(H){u=$jscomp.global;d=d.split(".");for(D=0;D<d.length-1;D++){var K=d[D];K in u||(u[K]={});u=u[K]}d=d[d.length-1];D=u[d];H=H(D);H!=D&&null!=H&&$jscomp.defineProperty(u,d,{configurable:!0,writable:!0,value:H})}};
$jscomp.polyfill("Array.prototype.find",function(d){return d?d:function(d,u){return $jscomp.findInternal(this,d,u).v}},"es6","es3");$jscomp.polyfill("Object.is",function(d){return d?d:function(d,u){return d===u?0!==d||1/d===1/u:d!==d&&u!==u}},"es6","es3");$jscomp.polyfill("Array.prototype.includes",function(d){return d?d:function(d,u){var D=this;D instanceof String&&(D=String(D));var H=D.length;for(u=u||0;u<H;u++)if(D[u]==d||Object.is(D[u],d))return!0;return!1}},"es7","es3");
$jscomp.checkStringArgs=function(d,H,u){if(null==d)throw new TypeError("The 'this' value for String.prototype."+u+" must not be null or undefined");if(H instanceof RegExp)throw new TypeError("First argument to String.prototype."+u+" must not be a regular expression");return d+""};$jscomp.polyfill("String.prototype.includes",function(d){return d?d:function(d,u){return-1!==$jscomp.checkStringArgs(this,d,"includes").indexOf(d,u||0)}},"es6","es3");
(function(d){function H(){return{theme:"striped",styles:{},headerStyles:{},bodyStyles:{},alternateRowStyles:{},columnStyles:{},startY:!1,margin:40,pageBreak:"auto",tableWidth:"auto",createdHeaderCell:function(d,u){},createdCell:function(d,u){},drawHeaderRow:function(d,u){},drawRow:function(d,u){},drawHeaderCell:function(d,u){},drawCell:function(d,u){},beforePageContent:function(d){},afterPageContent:function(d){}}}d.fn.tableExport=function(nb){function P(a){var c=[];T(a,"thead").each(function(){c.push.apply(c,
T(d(this),b.theadSelector).toArray())});return c}function Q(a){var c=[];T(a,"tbody").each(function(){c.push.apply(c,T(d(this),b.tbodySelector).toArray())});b.tfootSelector.length&&T(a,"tfoot").each(function(){c.push.apply(c,T(d(this),b.tfootSelector).toArray())});return c}function T(a,c){var b=a[0].tagName,h=a.parents(b).length;return a.find(c).filter(function(){return h===d(this).closest(b).parents(b).length})}function ya(a){var c=[],b=0,h=0,e=0;d(a).find("thead").first().find("th").each(function(a,
g){a=void 0!==d(g).attr("data-field");"undefined"!==typeof g.parentNode.rowIndex&&h!==g.parentNode.rowIndex&&(h=g.parentNode.rowIndex,b=e=0);var f=Y(g);for(b+=f?f:1;e<b;)c[e]=a?d(g).attr("data-field"):e.toString(),e++});return c}function N(a){var c="undefined"!==typeof a[0].rowIndex,b=!1===c&&"undefined"!==typeof a[0].cellIndex,h=b||c?ob(a):a.is(":visible"),e=a.attr("data-tableexport-display");b&&"none"!==e&&"always"!==e&&(a=d(a[0].parentNode),c="undefined"!==typeof a[0].rowIndex,e=a.attr("data-tableexport-display"));
c&&"none"!==e&&"always"!==e&&(e=a.closest("table").attr("data-tableexport-display"));return"none"!==e&&(!0===h||"always"===e)}function ob(a){var c=[];ia&&(c=U.filter(function(){var c=!1;this.nodeType===a[0].nodeType&&("undefined"!==typeof this.rowIndex&&this.rowIndex===a[0].rowIndex?c=!0:"undefined"!==typeof this.cellIndex&&this.cellIndex===a[0].cellIndex&&"undefined"!==typeof this.parentNode.rowIndex&&"undefined"!==typeof a[0].parentNode.rowIndex&&this.parentNode.rowIndex===a[0].parentNode.rowIndex&&
(c=!0));return c}));return!1===ia||0===c.length}function Ya(a,c,g){var h=!1;N(a)?0<b.ignoreColumn.length&&(-1!==d.inArray(g,b.ignoreColumn)||-1!==d.inArray(g-c,b.ignoreColumn)||fa.length>g&&"undefined"!==typeof fa[g]&&-1!==d.inArray(fa[g],b.ignoreColumn))&&(h=!0):h=!0;return h}function F(a,c,g,h,e){if("function"===typeof e){var v=!1;"function"===typeof b.onIgnoreRow&&(v=b.onIgnoreRow(d(a),g));if(!1===v&&(0===b.ignoreRow.length||-1===d.inArray(g,b.ignoreRow)&&-1===d.inArray(g-h,b.ignoreRow))&&N(d(a))){a=
T(d(a),c);var l=a.length,f=0,p=0;a.each(function(){var a=d(this),c=Y(this),b=ja(this),h;d.each(O,function(){if(g>this.s.r&&g<=this.e.r&&f>=this.s.c&&f<=this.e.c)for(h=0;h<=this.e.c-this.s.c;++h)l++,p++,e(null,g,f++)});if(b||c)c=c||1,O.push({s:{r:g,c:f},e:{r:g+(b||1)-1,c:f+c-1}});!1===Ya(a,l,p++)&&e(this,g,f++);if(1<c)for(h=0;h<c-1;++h)p++,e(null,g,f++)});d.each(O,function(){if(g>=this.s.r&&g<=this.e.r&&f>=this.s.c&&f<=this.e.c)for(var a=0;a<=this.e.c-this.s.c;++a)e(null,g,f++)})}}}function Za(a,c,
b,d){if("undefined"!==typeof d.images&&(b=d.images[b],"undefined"!==typeof b)){c=c.getBoundingClientRect();var g=a.width/a.height,h=c.width/c.height,l=a.width,f=a.height,p=19.049976/25.4,C=0;h<=g?(f=Math.min(a.height,c.height),l=c.width*f/c.height):h>g&&(l=Math.min(a.width,c.width),f=c.height*l/c.width);l*=p;f*=p;f<a.height&&(C=(a.height-f)/2);try{d.doc.addImage(b.src,a.textPos.x,a.y+C,l,f)}catch(B){}a.textPos.x+=l}}function $a(a,c){if("string"===b.outputMode)return a.output();if("base64"===b.outputMode)return R(a.output());
if("window"===b.outputMode)window.URL=window.URL||window.webkitURL,window.open(window.URL.createObjectURL(a.output("blob")));else{var g=b.fileName+".pdf";try{var d=a.output("blob");saveAs(d,g);if("function"===typeof b.onAfterSaveToFile)b.onAfterSaveToFile(d,g)}catch(e){Ia(g,"data:application/pdf"+(c?"":";base64")+",",c?a.output("blob"):a.output())}}}function ab(a,c,b){var g=0;"undefined"!==typeof b&&(g=b.colspan);if(0<=g){for(var d=a.width,v=a.textPos.x,l=c.table.columns.indexOf(c.column),f=1;f<g;f++)d+=
c.table.columns[l+f].width;1<g&&("right"===a.styles.halign?v=a.textPos.x+d-a.width:"center"===a.styles.halign&&(v=a.textPos.x+(d-a.width)/2));a.width=d;a.textPos.x=v;"undefined"!==typeof b&&1<b.rowspan&&(a.height*=b.rowspan);if("middle"===a.styles.valign||"bottom"===a.styles.valign)b=("string"===typeof a.text?a.text.split(/\r\n|\r|\n/g):a.text).length||1,2<b&&(a.textPos.y-=(2-1.15)/2*c.row.styles.fontSize*(b-2)/3);return!0}return!1}function bb(a,c,b){"undefined"!==typeof a&&null!==a&&(a.hasAttribute("data-tableexport-canvas")?
(c=(new Date).getTime(),d(a).attr("data-tableexport-canvas",c),b.images[c]={url:'[data-tableexport-canvas="'+c+'"]',src:null}):"undefined"!==c&&null!=c&&c.each(function(){if(d(this).is("img")){var c=cb(this.src);b.images[c]={url:this.src,src:this.src}}bb(a,d(this).children(),b)}))}function pb(a,c){function b(a){if(a.url)if(a.src){var b=new Image;h=++e;b.crossOrigin="Anonymous";b.onerror=b.onload=function(){if(b.complete&&(0===b.src.indexOf("data:image/")&&(b.width=a.width||b.width||0,b.height=a.height||
b.height||0),b.width+b.height)){var g=document.createElement("canvas"),d=g.getContext("2d");g.width=b.width;g.height=b.height;d.drawImage(b,0,0);a.src=g.toDataURL("image/png")}--e||c(h)};b.src=a.url}else{var g=d(a.url);g.length&&(h=++e,html2canvas(g[0]).then(function(b){a.src=b.toDataURL("image/png");--e||c(h)}))}}var h=0,e=0;if("undefined"!==typeof a.images)for(var v in a.images)a.images.hasOwnProperty(v)&&b(a.images[v]);(a=e)||(c(h),a=void 0);return a}function db(a,c,g){c.each(function(){if(d(this).is("div")){var c=
za(J(this,"background-color"),[255,255,255]),e=za(J(this,"border-top-color"),[0,0,0]),v=eb(this,"border-top-width",b.jspdf.unit),l=this.getBoundingClientRect(),f=this.offsetLeft*g.wScaleFactor,p=this.offsetTop*g.hScaleFactor,C=l.width*g.wScaleFactor;l=l.height*g.hScaleFactor;g.doc.setDrawColor.apply(void 0,e);g.doc.setFillColor.apply(void 0,c);g.doc.setLineWidth(v);g.doc.rect(a.x+f,a.y+p,C,l,v?"FD":"F")}else d(this).is("img")&&(c=cb(this.src),Za(a,this,c,g));db(a,d(this).children(),g)})}function fb(a,
c,g){if("function"===typeof g.onAutotableText)g.onAutotableText(g.doc,a,c);else{var h=a.textPos.x,e=a.textPos.y,v={halign:a.styles.halign,valign:a.styles.valign};if(c.length){for(c=c[0];c.previousSibling;)c=c.previousSibling;for(var l=!1,f=!1;c;){var p=c.innerText||c.textContent||"",C=p.length&&" "===p[0]?" ":"",B=1<p.length&&" "===p[p.length-1]?" ":"";!0!==b.preserve.leadingWS&&(p=C+Ja(p));!0!==b.preserve.trailingWS&&(p=Ka(p)+B);d(c).is("br")&&(h=a.textPos.x,e+=g.doc.internal.getFontSize());d(c).is("b")?
l=!0:d(c).is("i")&&(f=!0);(l||f)&&g.doc.setFont("undefined ",l&&f?"bolditalic":l?"bold":"italic");if(C=g.doc.getStringUnitWidth(p)*g.doc.internal.getFontSize()){"linebreak"===a.styles.overflow&&h>a.textPos.x&&h+C>a.textPos.x+a.width&&(0<=".,!%*;:=-".indexOf(p.charAt(0))&&(B=p.charAt(0),C=g.doc.getStringUnitWidth(B)*g.doc.internal.getFontSize(),h+C<=a.textPos.x+a.width&&(Aa(B,h,e,v),p=p.substring(1,p.length)),C=g.doc.getStringUnitWidth(p)*g.doc.internal.getFontSize()),h=a.textPos.x,e+=g.doc.internal.getFontSize());
if("visible"!==a.styles.overflow)for(;p.length&&h+C>a.textPos.x+a.width;)p=p.substring(0,p.length-1),C=g.doc.getStringUnitWidth(p)*g.doc.internal.getFontSize();Aa(p,h,e,v);h+=C}if(l||f)d(c).is("b")?l=!1:d(c).is("i")&&(f=!1),g.doc.setFont("undefined ",l||f?l?"bold":"italic":"normal");c=c.nextSibling}a.textPos.x=h;a.textPos.y=e}else Aa(a.text,a.textPos.x,a.textPos.y,v)}}function ka(a,c,b){return null==a?"":a.toString().replace(new RegExp(null==c?"":c.toString().replace(/([.*+?^=!:${}()|\[\]\/\\])/g,
"\\$1"),"g"),b)}function Ja(a){return null==a?"":a.toString().replace(/^\s+/,"")}function Ka(a){return null==a?"":a.toString().replace(/\s+$/,"")}function qb(a){if(0===b.date.html.length)return!1;b.date.pattern.lastIndex=0;var c=b.date.pattern.exec(a);if(null==c)return!1;a=+c[b.date.match_y];if(0>a||8099<a)return!1;var g=1*c[b.date.match_m];c=1*c[b.date.match_d];if(!isFinite(c))return!1;var d=new Date(a,g-1,c,0,0,0);return d.getFullYear()===a&&d.getMonth()===g-1&&d.getDate()===c?new Date(Date.UTC(a,
g-1,c,0,0,0)):!1}function La(a){a=a||"0";""!==b.numbers.html.thousandsSeparator&&(a=ka(a,b.numbers.html.thousandsSeparator,""));"."!==b.numbers.html.decimalMark&&(a=ka(a,b.numbers.html.decimalMark,"."));return"number"===typeof a||!1!==jQuery.isNumeric(a)?a:!1}function rb(a){-1<a.indexOf("%")?(a=La(a.replace(/%/g,"")),!1!==a&&(a/=100)):a=!1;return a}function E(a,c,g,h){var e="",v="text";if(null!==a){var l=d(a);l.removeData("teUserDefText");if(l[0].hasAttribute("data-tableexport-canvas"))var f="";else if(l[0].hasAttribute("data-tableexport-value"))f=
(f=l.attr("data-tableexport-value"))?f+"":"",l.data("teUserDefText",1);else if(f=l.html(),"function"===typeof b.onCellHtmlData)f=b.onCellHtmlData(l,c,g,f),l.data("teUserDefText",1);else if(""!==f){a=d.parseHTML("<div>"+f+"</div>",null,!1);var p=0,C=0;f="";d.each(a,function(){if(d(this).is("input"))f+=l.find("input").eq(p++).val();else if(d(this).is("select"))f+=l.find("select option:selected").eq(C++).text();else if(d(this).is("br"))f+="<br>";else{if("undefined"===typeof d(this).html())f+=d(this).text();
else if(void 0===jQuery().bootstrapTable||!1===d(this).hasClass("fht-cell")&&!1===d(this).hasClass("filterControl")&&0===l.parents(".detail-view").length)f+=d(this).html();if(d(this).is("a")){var a=l.find("a").attr("href")||"";e="function"===typeof b.onCellHtmlHyperlink?e+b.onCellHtmlHyperlink(l,c,g,a,f):"href"===b.htmlHyperlink?e+a:e+f;f=""}}})}if(f&&""!==f&&!0===b.htmlContent)e=d.trim(f);else if(f&&""!==f)if(""!==l.attr("data-tableexport-cellformat")){var B=f.replace(/\n/g,"\u2028").replace(/(<\s*br([^>]*)>)/gi,
"\u2060"),k=d("<div/>").html(B).contents();a=!1;B="";d.each(k.text().split("\u2028"),function(a,c){0<a&&(B+=" ");!0!==b.preserve.leadingWS&&(c=Ja(c));B+=!0!==b.preserve.trailingWS?Ka(c):c});d.each(B.split("\u2060"),function(a,c){0<a&&(e+="\n");!0!==b.preserve.leadingWS&&(c=Ja(c));!0!==b.preserve.trailingWS&&(c=Ka(c));e+=c.replace(/\u00AD/g,"")});e=e.replace(/\u00A0/g," ");if("json"===b.type||"excel"===b.type&&"xmlss"===b.mso.fileFormat||!1===b.numbers.output)a=La(e),!1!==a&&(v="number",e=Number(a));
else if(b.numbers.html.decimalMark!==b.numbers.output.decimalMark||b.numbers.html.thousandsSeparator!==b.numbers.output.thousandsSeparator)if(a=La(e),!1!==a){k=(""+a.substr(0>a?1:0)).split(".");1===k.length&&(k[1]="");var Ha=3<k[0].length?k[0].length%3:0;v="number";e=(0>a?"-":"")+(b.numbers.output.thousandsSeparator?(Ha?k[0].substr(0,Ha)+b.numbers.output.thousandsSeparator:"")+k[0].substr(Ha).replace(/(\d{3})(?=\d)/g,"$1"+b.numbers.output.thousandsSeparator):k[0])+(k[1].length?b.numbers.output.decimalMark+
k[1]:"")}}else e=f;!0===b.escape&&(e=escape(e));"function"===typeof b.onCellData&&(e=b.onCellData(l,c,g,e,v),l.data("teUserDefText",1))}void 0!==h&&(h.type=v);return e}function gb(a){return 0<a.length&&!0===b.preventInjection&&0<="=+-@".indexOf(a.charAt(0))?"'"+a:a}function sb(a,c,b){return c+"-"+b.toLowerCase()}function za(a,c){(a=/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/.exec(a))&&(c=[parseInt(a[1]),parseInt(a[2]),parseInt(a[3])]);return c}function Ma(a){var c=J(a,"text-align"),b=J(a,"font-weight"),
d=J(a,"font-style"),e="";"start"===c&&(c="rtl"===J(a,"direction")?"right":"left");700<=b&&(e="bold");"italic"===d&&(e+=d);""===e&&(e="normal");c={style:{align:c,bcolor:za(J(a,"background-color"),[255,255,255]),color:za(J(a,"color"),[0,0,0]),fstyle:e},colspan:Y(a),rowspan:ja(a)};null!==a&&(a=a.getBoundingClientRect(),c.rect={width:a.width,height:a.height});return c}function Y(a){var c=d(a).attr("data-tableexport-colspan");"undefined"===typeof c&&d(a).is("[colspan]")&&(c=d(a).attr("colspan"));return parseInt(c)||
0}function ja(a){var c=d(a).attr("data-tableexport-rowspan");"undefined"===typeof c&&d(a).is("[rowspan]")&&(c=d(a).attr("rowspan"));return parseInt(c)||0}function J(a,c){try{return window.getComputedStyle?(c=c.replace(/([a-z])([A-Z])/,sb),"object"===typeof a&&void 0!==a.nodeType?window.getComputedStyle(a,null).getPropertyValue(c):"object"===typeof a&&a.length?a.getPropertyValue(c):""):a.currentStyle?a.currentStyle[c]:a.style[c]}catch(g){}return""}function eb(a,c,b){c=J(a,c).match(/\d+/);if(null!==
c){c=c[0];a=a.parentElement;var d=document.createElement("div");d.style.overflow="hidden";d.style.visibility="hidden";a.appendChild(d);d.style.width=100+b;b=100/d.offsetWidth;a.removeChild(d);return c*b}return 0}function tb(a){for(var c=new ArrayBuffer(a.length),b=new Uint8Array(c),d=0;d!==a.length;++d)b[d]=a.charCodeAt(d)&255;return c}function Na(a){var c=a.c,b="";for(++c;c;c=Math.floor((c-1)/26))b=String.fromCharCode((c-1)%26+65)+b;return b+(""+(a.r+1))}function Oa(a,c){if("undefined"===typeof c||
"number"===typeof c)return Oa(a.s,a.e);"string"!==typeof a&&(a=Na(a));"string"!==typeof c&&(c=Na(c));return a===c?a:a+":"+c}function hb(a,c){var b=Number(a);if(isFinite(b))return b;var d=1;""!==c.thousandsSeparator&&(a=a.replace(new RegExp("([\\d])"+c.thousandsSeparator+"([\\d])","g"),"$1$2"));"."!==c.decimalMark&&(a=a.replace(new RegExp("([\\d])"+c.decimalMark+"([\\d])","g"),"$1.$2"));a=a.replace(/[$]/g,"").replace(/[%]/g,function(){d*=100;return""});if(isFinite(b=Number(a)))return b/d;a=a.replace(/[(](.*)[)]/,
function(a,c){d=-d;return c});return isFinite(b=Number(a))?b/d:b}function cb(a){var c=0,b;if(0===a.length)return c;var d=0;for(b=a.length;d<b;d++){var e=a.charCodeAt(d);c=(c<<5)-c+e;c|=0}return c}function S(a,c,d,h,e,v){var g=!0;"function"===typeof b.onBeforeSaveToFile&&(g=b.onBeforeSaveToFile(a,c,d,h,e),"boolean"!==typeof g&&(g=!0));if(g)try{if(ib=v?new Blob([String.fromCharCode(65279),[a]],{type:d+";charset="+h}):new Blob([a],{type:d+";charset="+h}),saveAs(ib,c,{autoBom:!1}),"function"===typeof b.onAfterSaveToFile)b.onAfterSaveToFile(a,
c)}catch(f){Ia(c,"data:"+d+(h.length?";charset="+h:"")+(e.length?";"+e:"")+",",v?"\ufeff"+a:a)}}function Ia(a,c,d){var g=window.navigator.userAgent;if(!1!==a&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(new Blob([d]),a);else if(!1!==a&&(0<g.indexOf("MSIE ")||g.match(/Trident.*rv\:11\./))){if(c=document.createElement("iframe")){document.body.appendChild(c);c.setAttribute("style","display:none");c.contentDocument.open("txt/plain","replace");c.contentDocument.write(d);c.contentDocument.close();
c.contentWindow.focus();switch(a.substr(a.lastIndexOf(".")+1)){case "doc":case "json":case "png":case "pdf":case "xls":case "xlsx":a+=".txt"}c.contentDocument.execCommand("SaveAs",!0,a);document.body.removeChild(c)}}else{var e=document.createElement("a");if(e){var v=null;e.style.display="none";!1!==a?e.download=a:e.target="_blank";"object"===typeof d?(window.URL=window.URL||window.webkitURL,g=[],g.push(d),v=window.URL.createObjectURL(new Blob(g,{type:c})),e.href=v):0<=c.toLowerCase().indexOf("base64,")?
e.href=c+R(d):e.href=c+encodeURIComponent(d);document.body.appendChild(e);if(document.createEvent)null===Ba&&(Ba=document.createEvent("MouseEvents")),Ba.initEvent("click",!0,!1),e.dispatchEvent(Ba);else if(document.createEventObject)e.fireEvent("onclick");else if("function"===typeof e.onclick)e.onclick();setTimeout(function(){v&&window.URL.revokeObjectURL(v);document.body.removeChild(e);if("function"===typeof b.onAfterSaveToFile)b.onAfterSaveToFile(d,a)},100)}}}function R(a){var c,b="",d=0;if("string"===
typeof a){a=a.replace(/\x0d\x0a/g,"\n");var e="";for(c=0;c<a.length;c++){var v=a.charCodeAt(c);128>v?e+=String.fromCharCode(v):(127<v&&2048>v?e+=String.fromCharCode(v>>6|192):(e+=String.fromCharCode(v>>12|224),e+=String.fromCharCode(v>>6&63|128)),e+=String.fromCharCode(v&63|128))}a=e}for(;d<a.length;){var l=a.charCodeAt(d++);e=a.charCodeAt(d++);c=a.charCodeAt(d++);v=l>>2;l=(l&3)<<4|e>>4;var f=(e&15)<<2|c>>6;var p=c&63;isNaN(e)?f=p=64:isNaN(c)&&(p=64);b=b+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(v)+
"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(l)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(f)+"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".charAt(p)}return b}function ub(a,c,b,d){c&&"object"===typeof c||console.error("The headers should be an object or array, is: "+typeof c);b&&"object"===typeof b||console.error("The data should be an object or array, is: "+typeof b);d&&"object"!==typeof d&&console.error("The data should be an object or array, is: "+
typeof b);Array.prototype.forEach||console.error("The current browser does not support Array.prototype.forEach which is required for jsPDF-AutoTable");y=a;m=vb(d||{});Pa=1;I={y:!1===m.startY?m.margin.top:m.startY};a={textColor:30,fontSize:y.internal.getFontSize(),fontStyle:y.internal.getFont().fontStyle,fontName:y.internal.getFont().fontName};wb(c,b);xb();c=m.startY+m.margin.bottom+r.headerRow.height+(r.rows[0]&&"auto"===m.pageBreak?r.rows[0].height:0);"avoid"===m.pageBreak&&(c+=r.height);if("always"===
m.pageBreak&&!1!==m.startY||!1!==m.startY&&c>y.internal.pageSize.height)y.addPage(),I.y=m.margin.top;la(a);m.beforePageContent(V());!1!==m.drawHeaderRow(r.headerRow,V({row:r.headerRow}))&&Qa(r.headerRow,m.drawHeaderCell);la(a);yb();m.afterPageContent(V());la(a);return y}function Aa(a,c,b,d){"number"===typeof c&&"number"===typeof b||console.error("The x and y parameters are required. Missing for the text: ",a);var g=y.internal.getFontSize()/y.internal.scaleFactor,h=/\r\n|\r|\n/g,l=null,f=1;if("middle"===
d.valign||"bottom"===d.valign||"center"===d.halign||"right"===d.halign)l="string"===typeof a?a.split(h):a,f=l.length||1;b+=g*(2-1.15);"middle"===d.valign?b-=f/2*g:"bottom"===d.valign&&(b-=f*g);if("center"===d.halign||"right"===d.halign){h=g;"center"===d.halign&&(h*=.5);if(l&&1<=f){for(a=0;a<l.length;a++)y.text(l[a],c-y.getStringUnitWidth(l[a])*h,b),b+=g;return y}c-=y.getStringUnitWidth(a)*h}y.text(a,c,b);return y}function vb(a){var c=Z(H(),a);"undefined"!==typeof c.extendWidth&&(c.tableWidth=c.extendWidth?
"auto":"wrap",console.error("Use of deprecated option: extendWidth, use tableWidth instead."));"undefined"!==typeof c.margins&&("undefined"===typeof c.margin&&(c.margin=c.margins),console.error("Use of deprecated option: margins, use margin instead."));[["padding","cellPadding"],["lineHeight","rowHeight"],"fontSize","overflow"].forEach(function(a){var b="string"===typeof a?a:a[0];a="string"===typeof a?a:a[1];"undefined"!==typeof c[b]&&("undefined"===typeof c.styles[a]&&(c.styles[a]=c[b]),console.error("Use of deprecated option: "+
b+", use the style "+a+" instead."))});var b=c.margin;c.margin={};"number"===typeof b.horizontal&&(b.right=b.horizontal,b.left=b.horizontal);"number"===typeof b.vertical&&(b.top=b.vertical,b.bottom=b.vertical);["top","right","bottom","left"].forEach(function(a,d){"number"===typeof b?c.margin[a]=b:(d=Array.isArray(b)?d:a,c.margin[a]="number"===typeof b[d]?b[d]:40)});return c}function wb(a,b){r=new u;r.x=m.margin.left;var c=/\r\n|\r|\n/g,h=new D(a);h.index=-1;var e=Z(Ca,pa[m.theme].table,pa[m.theme].header);
h.styles=Z(e,m.styles,m.headerStyles);a.forEach(function(a,b){"object"===typeof a&&(b="undefined"!==typeof a.dataKey?a.dataKey:a.key);"undefined"!==typeof a.width&&console.error("Use of deprecated option: column.width, use column.styles.columnWidth instead.");var f=new xa(b);f.styles=m.columnStyles[f.dataKey]||{};r.columns.push(f);var g=new K;g.raw="object"===typeof a?a.title:a;g.styles=d.extend({},h.styles);g.text=""+g.raw;g.contentWidth=2*g.styles.cellPadding+Da(g.text,g.styles);g.text=g.text.split(c);
h.cells[b]=g;m.createdHeaderCell(g,{column:f,row:h,settings:m})});r.headerRow=h;b.forEach(function(a,b){var d=new D(a),g=0===b%2,e=Z(Ca,pa[m.theme].table,g?pa[m.theme].alternateRow:{});g=Z(m.styles,m.bodyStyles,g?m.alternateRowStyles:{});d.styles=Z(e,g);d.index=b;r.columns.forEach(function(b){var f=new K;f.raw=a[b.dataKey];f.styles=Z(d.styles,b.styles);f.text="undefined"!==typeof f.raw?""+f.raw:"";d.cells[b.dataKey]=f;m.createdCell(f,V({column:b,row:d}));f.contentWidth=2*f.styles.cellPadding+Da(f.text,
f.styles);f.text=f.text.split(c)});r.rows.push(d)})}function xb(){var a=0;r.columns.forEach(function(b){b.contentWidth=r.headerRow.cells[b.dataKey].contentWidth;r.rows.forEach(function(a){a=a.cells[b.dataKey].contentWidth;a>b.contentWidth&&(b.contentWidth=a)});b.width=b.contentWidth;a+=b.contentWidth});r.contentWidth=a;var b=y.internal.pageSize.width-m.margin.left-m.margin.right,d=b;"number"===typeof m.tableWidth?d=m.tableWidth:"wrap"===m.tableWidth&&(d=r.contentWidth);r.width=d<b?d:b;var h=[],e=
0,v=r.width/r.columns.length,l=0;r.columns.forEach(function(a){var b=Z(Ca,pa[m.theme].table,m.styles,a.styles);"wrap"===b.columnWidth?a.width=a.contentWidth:"number"===typeof b.columnWidth?a.width=b.columnWidth:a.contentWidth<=v&&r.contentWidth>r.width?a.width=a.contentWidth:(h.push(a),e+=a.contentWidth,a.width=0);l+=a.width});jb(h,l,e,v);r.height=0;r.rows.concat(r.headerRow).forEach(function(a,b){var c=0,d=r.x;r.columns.forEach(function(b){var f=a.cells[b.dataKey];b.x=d;la(f.styles);var g=b.width-
2*f.styles.cellPadding;"linebreak"===f.styles.overflow?f.text=y.splitTextToSize(f.text,g+1,{fontSize:f.styles.fontSize}):"ellipsize"===f.styles.overflow?f.text=Ra(f.text,g,f.styles):"visible"!==f.styles.overflow&&("hidden"===f.styles.overflow?f.text=Ra(f.text,g,f.styles,""):"function"===typeof f.styles.overflow?f.text=f.styles.overflow(f.text,g):console.error("Unrecognized overflow type: "+f.styles.overflow));f=Array.isArray(f.text)?f.text.length-1:0;f>c&&(c=f);d+=b.width});a.heightStyle=a.styles.rowHeight;
a.height=a.heightStyle+c*a.styles.fontSize*1.15+(2-1.15)/2*a.styles.fontSize;r.height+=a.height})}function jb(a,b,d,h){for(var c=r.width-b-d,g=0;g<a.length;g++){var l=a[g],f=l.contentWidth/d,p=l.contentWidth+c*f<h;if(0>c&&p){a.splice(g,1);d-=l.contentWidth;l.width=h;b+=l.width;jb(a,b,d,h);break}else l.width=l.contentWidth+c*f}}function yb(){r.rows.forEach(function(a,b){I.y+a.height+m.margin.bottom>=y.internal.pageSize.height&&(m.afterPageContent(V()),y.addPage(),Pa++,I={x:m.margin.left,y:m.margin.top},
m.beforePageContent(V()),!1!==m.drawHeaderRow(r.headerRow,V({row:r.headerRow}))&&Qa(r.headerRow,m.drawHeaderCell));a.y=I.y;!1!==m.drawRow(a,V({row:a}))&&Qa(a,m.drawCell)})}function Qa(a,b){for(var c=0;c<r.columns.length;c++){var d=r.columns[c],e=a.cells[d.dataKey];e&&(la(e.styles),e.x=d.x,e.y=I.y,e.height=a.height,e.width=d.width,e.textPos.y="top"===e.styles.valign?I.y+e.styles.cellPadding:"bottom"===e.styles.valign?I.y+a.height-e.styles.cellPadding:I.y+a.height/2,e.textPos.x="right"===e.styles.halign?
e.x+e.width-e.styles.cellPadding:"center"===e.styles.halign?e.x+e.width/2:e.x+e.styles.cellPadding,d=V({column:d,row:a}),!1!==b(e,d)&&(y.rect(e.x,e.y,e.width,e.height,e.styles.fillStyle),Aa(e.text,e.textPos.x,e.textPos.y,{halign:e.styles.halign,valign:e.styles.valign})))}I.y+=a.height}function la(a){[{func:y.setFillColor,value:a.fillColor},{func:y.setTextColor,value:a.textColor},{func:y.setFont,value:a.font,style:a.fontStyle},{func:y.setDrawColor,value:a.lineColor},{func:y.setLineWidth,value:a.lineWidth},
{func:y.setFont,value:a.font},{func:y.setFontSize,value:a.fontSize}].forEach(function(a){"undefined"!==typeof a.value&&(a.value.constructor===Array?a.func.apply(y,a.value):"undefined"!==typeof a.style?a.func(a.value,a.style):a.func(a.value))})}function V(a){a=a||{};var b={pageCount:Pa,settings:m,table:r,cursor:I},d;for(d in a)a.hasOwnProperty(d)&&(b[d]=a[d]);return b}function Ra(a,b,d,h){h="undefined"!==typeof h?h:"...";if(Array.isArray(a))return a.forEach(function(c,g){a[g]=Ra(c,b,d,h)}),a;if(b>=
Da(a,d))return a;for(;b<Da(a+h,d)&&!(2>a.length);)a=a.substring(0,a.length-1);return a.trim()+h}function Da(a,b){la(b);return y.getStringUnitWidth(a)*b.fontSize}function Z(a){var b={},d;for(d in a)a.hasOwnProperty(d)&&(b[d]=a[d]);for(var h=1;h<arguments.length;h++){var e=arguments[h];for(d in e)e.hasOwnProperty(d)&&(b[d]=e[d])}return b}var L,b={csvEnclosure:'"',csvSeparator:",",csvUseBOM:!0,date:{html:"dd/mm/yyyy"},displayTableName:!1,escape:!1,exportHiddenCells:!1,fileName:"tableExport",htmlContent:!1,
htmlHyperlink:"content",ignoreColumn:[],ignoreRow:[],jsonScope:"all",jspdf:{orientation:"p",unit:"pt",format:"a4",margins:{left:20,right:10,top:10,bottom:10},onDocCreated:null,autotable:{styles:{cellPadding:2,rowHeight:12,fontSize:8,fillColor:255,textColor:50,fontStyle:"normal",overflow:"ellipsize",halign:"inherit",valign:"middle"},headerStyles:{fillColor:[52,73,94],textColor:255,fontStyle:"bold",halign:"inherit",valign:"middle"},alternateRowStyles:{fillColor:245},tableExport:{doc:null,onAfterAutotable:null,
onBeforeAutotable:null,onAutotableText:null,onTable:null,outputImages:!0}}},mso:{fileFormat:"xlshtml",onMsoNumberFormat:null,pageFormat:"a4",pageOrientation:"portrait",rtl:!1,styles:[],worksheetName:"",xlsx:{formatId:{date:14,numbers:2,currency:164},format:{currency:"$#,##0.00;[Red]-$#,##0.00"},onHyperlink:null}},numbers:{html:{decimalMark:".",thousandsSeparator:","},output:{decimalMark:".",thousandsSeparator:","}},onAfterSaveToFile:null,onBeforeSaveToFile:null,onCellData:null,onCellHtmlData:null,
onCellHtmlHyperlink:null,onIgnoreRow:null,onTableExportBegin:null,onTableExportEnd:null,outputMode:"file",pdfmake:{enabled:!1,docDefinition:{pageSize:"A4",pageOrientation:"portrait",styles:{header:{background:"#34495E",color:"#FFFFFF",bold:!0,alignment:"center",fillColor:"#34495E"},alternateRow:{fillColor:"#f5f5f5"}},defaultStyle:{color:"#000000",fontSize:8,font:"Roboto"}},fonts:{},widths:"*"},preserve:{leadingWS:!1,trailingWS:!1},preventInjection:!0,sql:{tableEnclosure:"`",columnEnclosure:"`"},tbodySelector:"tr",
tfootSelector:"tr",theadSelector:"tr",tableName:"Table",type:"csv"},W={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,
3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]},pa={striped:{table:{fillColor:255,textColor:80,fontStyle:"normal",fillStyle:"F"},header:{textColor:255,fillColor:[41,128,185],rowHeight:23,
fontStyle:"bold"},body:{},alternateRow:{fillColor:245}},grid:{table:{fillColor:255,textColor:80,fontStyle:"normal",lineWidth:.1,fillStyle:"DF"},header:{textColor:255,fillColor:[26,188,156],rowHeight:23,fillStyle:"F",fontStyle:"bold"},body:{},alternateRow:{}},plain:{header:{fontStyle:"bold"}}},Ca={cellPadding:5,fontSize:10,fontName:"helvetica",lineColor:200,lineWidth:.1,fontStyle:"normal",overflow:"ellipsize",fillColor:255,textColor:20,halign:"left",valign:"top",fillStyle:"F",rowHeight:20,columnWidth:"auto"},
x=this,Ba=null,z=[],t=[],q=0,w="",fa=[],O=[],ib,U=[],ia=!1;d.extend(!0,b,nb);"xlsx"===b.type&&(b.mso.fileFormat=b.type,b.type="excel");"undefined"!==typeof b.excelFileFormat&&"undefined"===typeof b.mso.fileFormat&&(b.mso.fileFormat=b.excelFileFormat);"undefined"!==typeof b.excelPageFormat&&"undefined"===typeof b.mso.pageFormat&&(b.mso.pageFormat=b.excelPageFormat);"undefined"!==typeof b.excelPageOrientation&&"undefined"===typeof b.mso.pageOrientation&&(b.mso.pageOrientation=b.excelPageOrientation);
"undefined"!==typeof b.excelRTL&&"undefined"===typeof b.mso.rtl&&(b.mso.rtl=b.excelRTL);"undefined"!==typeof b.excelstyles&&"undefined"===typeof b.mso.styles&&(b.mso.styles=b.excelstyles);"undefined"!==typeof b.onMsoNumberFormat&&"undefined"===typeof b.mso.onMsoNumberFormat&&(b.mso.onMsoNumberFormat=b.onMsoNumberFormat);"undefined"!==typeof b.worksheetName&&"undefined"===typeof b.mso.worksheetName&&(b.mso.worksheetName=b.worksheetName);"undefined"!==typeof b.mso.xslx&&"undefined"===typeof b.mso.xlsx&&
(b.mso.xlsx=b.mso.xslx);b.mso.pageOrientation="l"===b.mso.pageOrientation.substr(0,1)?"landscape":"portrait";b.date.html=b.date.html||"";if(b.date.html.length){var ha=[];ha.dd="(3[01]|[12][0-9]|0?[1-9])";ha.mm="(1[012]|0?[1-9])";ha.yyyy="((?:1[6-9]|2[0-2])\\d{2})";ha.yy="(\\d{2})";var zb=b.date.html.match(/[^a-zA-Z0-9]/)[0],aa=b.date.html.toLowerCase().split(zb);b.date.regex="^\\s*";b.date.regex+=ha[aa[0]];b.date.regex+="(.)";b.date.regex+=ha[aa[1]];b.date.regex+="\\2";b.date.regex+=ha[aa[2]];b.date.regex+=
"\\s*$";b.date.pattern=new RegExp(b.date.regex,"g");var ba=aa.indexOf("dd")+1;b.date.match_d=ba+(1<ba?1:0);ba=aa.indexOf("mm")+1;b.date.match_m=ba+(1<ba?1:0);ba=(0<=aa.indexOf("yyyy")?aa.indexOf("yyyy"):aa.indexOf("yy"))+1;b.date.match_y=ba+(1<ba?1:0)}fa=ya(x);if("function"===typeof b.onTableExportBegin)b.onTableExportBegin();if("csv"===b.type||"tsv"===b.type||"txt"===b.type){var ca="",qa=0;O=[];q=0;var Sa=function(a,c,g){a.each(function(){w="";F(this,c,q,g+a.length,function(a,c,d){var g=w,f="";if(null!==
a)if(a=E(a,c,d),c=null===a||""===a?"":a.toString(),"tsv"===b.type)a instanceof Date&&a.toLocaleString(),f=ka(c,"\t"," ");else if(a instanceof Date)f=b.csvEnclosure+a.toLocaleString()+b.csvEnclosure;else if(f=gb(c),f=ka(f,b.csvEnclosure,b.csvEnclosure+b.csvEnclosure),0<=f.indexOf(b.csvSeparator)||/[\r\n ]/g.test(f))f=b.csvEnclosure+f+b.csvEnclosure;w=g+(f+("tsv"===b.type?"\t":b.csvSeparator))});w=d.trim(w).substring(0,w.length-1);0<w.length&&(0<ca.length&&(ca+="\n"),ca+=w);q++});return a.length};qa+=
Sa(d(x).find("thead").first().find(b.theadSelector),"th,td",qa);T(d(x),"tbody").each(function(){qa+=Sa(T(d(this),b.tbodySelector),"td,th",qa)});b.tfootSelector.length&&Sa(d(x).find("tfoot").first().find(b.tfootSelector),"td,th",qa);ca+="\n";if("string"===b.outputMode)return ca;if("base64"===b.outputMode)return R(ca);if("window"===b.outputMode){Ia(!1,"data:text/"+("csv"===b.type?"csv":"plain")+";charset=utf-8,",ca);return}S(ca,b.fileName+"."+b.type,"text/"+("csv"===b.type?"csv":"plain"),"utf-8","",
"csv"===b.type&&b.csvUseBOM)}else if("markdown"===b.type){var Ab=function(a){var b="",g=P(a),h=Q(a);a=0<g.length;var e="|",k="|";a?d(g).each(function(){F(this,"th,td",q,g.length,function(a,b,c){a=E(a,b,c);a=a.replace(/\|/g,"\\|").replace(/-/g,"\\-").replace(/\*/g,"\\*").replace(/_/g,"\\_").replace(/\#/g,"\\#");a=a.replace(/<br\s*\/?>/gi," ");a=a.replace(/\r/g,"").replace(/\t/g,"    ").replace(/\n/g," ");a=d.trim(a);e+=" "+a+" |"});q++}):(k=h[0])&&F(k,"td",q,h.length,function(a,b,c){e+="  |"});k="|";
for(var l=0;l<e.split("|").length-2;l++)k+=" --- |";if(a||0<h.length)b+=e+"\n"+k+"\n";d(h).each(function(){var a="|";F(this,"td",q,h.length,function(b,c,f){b=E(b,c,f);b=b.replace(/\|/g,"\\|").replace(/-/g,"\\-").replace(/\*/g,"\\*").replace(/_/g,"\\_").replace(/\#/g,"\\#");b=b.replace(/<br\s*\/?>/gi," ");b=b.replace(/\r/g,"").replace(/\t/g,"    ").replace(/\n/g," ");b=d.trim(b);a+=" "+b+" |"});"|"!==a&&(b+=a+"\n");q++});return b},ra="";P(d(x));Q(d(x));d(x).filter(function(){return N(d(this))}).each(function(){var a=
Ab(d(this));ra+=a;d(x).index(this)<d(x).length-1&&(ra+="\n\n")});if("string"===b.outputMode)return ra;if("base64"===b.outputMode)return R(ra);S(ra,b.fileName+".md","text/markdown","utf-8","",!1)}else if("sql"===b.type){q=0;O=[];var G="INSERT INTO "+b.sql.tableEnclosure+b.tableName+b.sql.tableEnclosure+" (";z=P(d(x));d(z).each(function(){F(this,"th,td",q,z.length,function(a,c,d){a=E(a,c,d)||"";-1<a.indexOf(b.sql.columnEnclosure)&&(a=ka(a.toString(),b.sql.columnEnclosure,b.sql.columnEnclosure+b.sql.columnEnclosure));
G+=b.sql.columnEnclosure+a+b.sql.columnEnclosure+","});q++;G=d.trim(G).substring(0,G.length-1)});G+=") VALUES ";t=Q(d(x));d(t).each(function(){w="";F(this,"td,th",q,z.length+t.length,function(a,b,d){a=E(a,b,d)||"";-1<a.indexOf("'")&&(a=ka(a.toString(),"'","''"));w+="'"+a+"',"});3<w.length&&(G+="("+w,G=d.trim(G).substring(0,G.length-1),G+="),");q++});G=d.trim(G).substring(0,G.length-1);G+=";";if("string"===b.outputMode)return G;if("base64"===b.outputMode)return R(G);S(G,b.fileName+".sql","application/sql",
"utf-8","",!1)}else if("json"===b.type){var ma=[];O=[];z=P(d(x));d(z).each(function(){var a=[];F(this,"th,td",q,z.length,function(b,d,h){a.push(E(b,d,h))});ma.push(a)});var Ta=[];t=Q(d(x));d(t).each(function(){var a={},b=0;F(this,"td,th",q,z.length+t.length,function(c,d,e){ma.length?a[ma[ma.length-1][b]]=E(c,d,e):a[b]=E(c,d,e);b++});!1===d.isEmptyObject(a)&&Ta.push(a);q++});var Ua="head"===b.jsonScope?JSON.stringify(ma):"data"===b.jsonScope?JSON.stringify(Ta):JSON.stringify({header:ma,data:Ta});if("string"===
b.outputMode)return Ua;if("base64"===b.outputMode)return R(Ua);S(Ua,b.fileName+".json","application/json","utf-8","base64",!1)}else if("xml"===b.type){q=0;O=[];var da='<?xml version="1.0" encoding="utf-8"?>';da+="<tabledata><fields>";z=P(d(x));d(z).each(function(){F(this,"th,td",q,z.length,function(a,b,d){da+="<field>"+E(a,b,d)+"</field>"});q++});da+="</fields><data>";var kb=1;t=Q(d(x));d(t).each(function(){var a=1;w="";F(this,"td,th",q,z.length+t.length,function(b,d,h){w+="<column-"+a+">"+E(b,d,
h)+"</column-"+a+">";a++});0<w.length&&"<column-1></column-1>"!==w&&(da+='<row id="'+kb+'">'+w+"</row>",kb++);q++});da+="</data></tabledata>";if("string"===b.outputMode)return da;if("base64"===b.outputMode)return R(da);S(da,b.fileName+".xml","application/xml","utf-8","base64",!1)}else if("excel"===b.type&&"xmlss"===b.mso.fileFormat){var Va=[],M=[];d(x).filter(function(){return N(d(this))}).each(function(){function a(a,b,c){var g=[];d(a).each(function(){var b=0,e=0;w="";F(this,"td,th",q,c+a.length,
function(a,c,f){if(null!==a){var l="";c=E(a,c,f);f="String";if(!1!==jQuery.isNumeric(c))f="Number";else{var h=rb(c);!1!==h&&(c=h,f="Number",l+=' ss:StyleID="pct1"')}"Number"!==f&&(c=c.replace(/\n/g,"<br>"));h=Y(a);a=ja(a);d.each(g,function(){if(q>=this.s.r&&q<=this.e.r&&e>=this.s.c&&e<=this.e.c)for(var a=0;a<=this.e.c-this.s.c;++a)e++,b++});if(a||h)a=a||1,h=h||1,g.push({s:{r:q,c:e},e:{r:q+a-1,c:e+h-1}});1<h&&(l+=' ss:MergeAcross="'+(h-1)+'"',e+=h-1);1<a&&(l+=' ss:MergeDown="'+(a-1)+'" ss:StyleID="rsp1"');
0<b&&(l+=' ss:Index="'+(e+1)+'"',b=0);w+="<Cell"+l+'><Data ss:Type="'+f+'">'+d("<div />").text(c).html()+"</Data></Cell>\r";e++}});0<w.length&&(L+='<Row ss:AutoFitHeight="0">\r'+w+"</Row>\r");q++});return a.length}var c=d(this),g="";"string"===typeof b.mso.worksheetName&&b.mso.worksheetName.length?g=b.mso.worksheetName+" "+(M.length+1):"undefined"!==typeof b.mso.worksheetName[M.length]&&(g=b.mso.worksheetName[M.length]);g.length||(g=c.find("caption").text()||"");g.length||(g="Table "+(M.length+1));
g=d.trim(g.replace(/[\\\/[\]*:?'"]/g,"").substring(0,31));M.push(d("<div />").text(g).html());!1===b.exportHiddenCells&&(U=c.find("tr, th, td").filter(":hidden"),ia=0<U.length);q=0;fa=ya(this);L="<Table>\r";g=a(P(c),"th,td",0);a(Q(c),"td,th",g);L+="</Table>\r";Va.push(L)});for(var Ea={},Wa={},ea,sa,na=0,Bb=M.length;na<Bb;na++)ea=M[na],sa=Ea[ea],sa=Ea[ea]=null==sa?1:sa+1,2===sa&&(M[Wa[ea]]=M[Wa[ea]].substring(0,29)+"-1"),1<Ea[ea]?M[na]=M[na].substring(0,29)+"-"+Ea[ea]:Wa[ea]=na;for(var X='<?xml version="1.0" encoding="UTF-8"?>\r<?mso-application progid="Excel.Sheet"?>\r<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"\r xmlns:o="urn:schemas-microsoft-com:office:office"\r xmlns:x="urn:schemas-microsoft-com:office:excel"\r xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"\r xmlns:html="http://www.w3.org/TR/REC-html40">\r<DocumentProperties xmlns="urn:schemas-microsoft-com:office:office">\r  <Created>'+
(new Date).toISOString()+'</Created>\r</DocumentProperties>\r<OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office">\r  <AllowPNG/>\r</OfficeDocumentSettings>\r<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel">\r  <WindowHeight>9000</WindowHeight>\r  <WindowWidth>13860</WindowWidth>\r  <WindowTopX>0</WindowTopX>\r  <WindowTopY>0</WindowTopY>\r  <ProtectStructure>False</ProtectStructure>\r  <ProtectWindows>False</ProtectWindows>\r</ExcelWorkbook>\r<Styles>\r  <Style ss:ID="Default" ss:Name="Normal">\r    <Alignment ss:Vertical="Bottom"/>\r    <Borders/>\r    <Font/>\r    <Interior/>\r    <NumberFormat/>\r    <Protection/>\r  </Style>\r  <Style ss:ID="rsp1">\r    <Alignment ss:Vertical="Center"/>\r  </Style>\r  <Style ss:ID="pct1">\r    <NumberFormat ss:Format="Percent"/>\r  </Style>\r</Styles>\r',
Fa=0;Fa<Va.length;Fa++)X+='<Worksheet ss:Name="'+M[Fa]+'" ss:RightToLeft="'+(b.mso.rtl?"1":"0")+'">\r'+Va[Fa],X=b.mso.rtl?X+'<WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel">\r<DisplayRightToLeft/>\r</WorksheetOptions>\r':X+'<WorksheetOptions xmlns="urn:schemas-microsoft-com:office:excel"/>\r',X+="</Worksheet>\r";X+="</Workbook>\r";if("string"===b.outputMode)return X;if("base64"===b.outputMode)return R(X);S(X,b.fileName+".xml","application/xml","utf-8","base64",!1)}else if("excel"===
b.type&&"xlsx"===b.mso.fileFormat){var ta=[],lb=XLSX.utils.book_new();d(x).filter(function(){return N(d(this))}).each(function(){for(var a=d(this),c,g={},h=this.getElementsByTagName("tr"),e=Math.min(1E7,h.length),k={s:{r:0,c:0},e:{r:0,c:0}},l=[],f,p=0,C=0,B,m,q,r,n,u=XLSX.SSF.get_table();p<h.length&&C<e;++p)if(B=h[p],m=!1,"function"===typeof b.onIgnoreRow&&(m=b.onIgnoreRow(d(B),p)),!0!==m&&(0===b.ignoreRow.length||-1===d.inArray(p,b.ignoreRow)&&-1===d.inArray(p-h.length,b.ignoreRow))&&!1!==N(d(B))){var w=
B.children,z=0;for(B=0;B<w.length;++B)n=w[B],r=+Y(n)||1,z+=r;var y=0;for(B=m=0;B<w.length;++B)if(n=w[B],r=+Y(n)||1,f=B+y,!Ya(d(n),z,f+(f<m?m-f:0))){y+=r-1;for(f=0;f<l.length;++f){var A=l[f];A.s.c==m&&A.s.r<=C&&C<=A.e.r&&(m=A.e.c+1,f=-1)}(0<(q=+ja(n))||1<r)&&l.push({s:{r:C,c:m},e:{r:C+(q||1)-1,c:m+r-1}});var x={type:""};A=E(n,p,B+y,x);f={t:"s",v:A};var t="";if(""!==(d(n).attr("data-tableexport-cellformat")||void 0))if(c=parseInt(d(n).attr("data-tableexport-xlsxformatid")||0),0===c&&"function"===typeof b.mso.xlsx.formatId.numbers&&
(c=b.mso.xlsx.formatId.numbers(d(n),p,B+y)),0===c&&"function"===typeof b.mso.xlsx.formatId.date&&(c=b.mso.xlsx.formatId.date(d(n),p,B+y)),49===c||"@"===c)t="s";else if("number"===x.type||0<c&&14>c||36<c&&41>c||48===c)t="n";else{if("date"===x.type||13<c&&37>c||44<c&&48>c||56===c)t="d"}else t="s";if(null!==A&&void 0!==A){if("string"===typeof A&&0===A.length)f.t="z";else if(("string"!==typeof A||0!==A.trim().length)&&"s"!==t)if("function"===x.type)f={f:A};else if("string"===typeof A&&"TRUE"===A.toUpperCase())f=
{t:"b",v:!0};else if("string"===typeof A&&"FALSE"===A.toUpperCase())f={t:"b",v:!1};else if("n"===t||isFinite(hb(A,b.numbers.output))){if(t=hb(A,b.numbers.output),0===c&&"function"!==typeof b.mso.xlsx.formatId.numbers&&(c=b.mso.xlsx.formatId.numbers),isFinite(t)||isFinite(A))f={t:"n",v:isFinite(t)?t:A,z:"string"===typeof c?c:c in u?u[c]:c===b.mso.xlsx.formatId.currency?b.mso.xlsx.format.currency:"0.00"}}else if(!1!==(x=qb(A))||"d"===t)0===c&&"function"!==typeof b.mso.xlsx.formatId.date&&(c=b.mso.xlsx.formatId.date),
f={t:"d",v:!1!==x?x:A,z:"string"===typeof c?c:c in u?u[c]:"m/d/yy"};(t=d(n).find("a"))&&t.length&&(t=t[0].hasAttribute("href")?t.attr("href"):"",A="href"!==b.htmlHyperlink||""===t?A:"",x=""!==t?'=HYPERLINK("'+t+(A.length?'","'+A:"")+'")':"",""!==x&&("function"===typeof b.mso.xlsx.onHyperlink?(A=b.mso.xlsx.onHyperlink(d(n),p,B,t,A,x),f=0!==A.indexOf("=HYPERLINK")?{t:"s",v:A}:{f:A}):f={f:x}))}g[Na({c:m,r:C})]=f;k.e.c<m&&(k.e.c=m);m+=r}++C}l.length&&(g["!merges"]=(g["!merges"]||[]).concat(l));k.e.r=
Math.max(k.e.r,C-1);g["!ref"]=Oa(k);C>=e&&(g["!fullref"]=Oa((k.e.r=h.length-p+C-1,k)));c="";"string"===typeof b.mso.worksheetName&&b.mso.worksheetName.length?c=b.mso.worksheetName+" "+(ta.length+1):"undefined"!==typeof b.mso.worksheetName[ta.length]&&(c=b.mso.worksheetName[ta.length]);c.length||(c=a.find("caption").text()||"");c.length||(c="Table "+(ta.length+1));c=d.trim(c.replace(/[\\\/[\]*:?'"]/g,"").substring(0,31));ta.push(c);XLSX.utils.book_append_sheet(lb,g,c)});var Cb=XLSX.write(lb,{type:"binary",
bookType:b.mso.fileFormat,bookSST:!1});S(tb(Cb),b.fileName+"."+b.mso.fileFormat,"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","UTF-8","",!1)}else if("excel"===b.type||"xls"===b.type||"word"===b.type||"doc"===b.type){var ua="excel"===b.type||"xls"===b.type?"excel":"word",Db="excel"===ua?"xls":"doc",Eb='xmlns:x="urn:schemas-microsoft-com:office:'+ua+'"',va=L="";d(x).filter(function(){return N(d(this))}).each(function(){var a=d(this);""===va&&(va=b.mso.worksheetName||a.find("caption").text()||
"Table",va=d.trim(va.replace(/[\\\/[\]*:?'"]/g,"").substring(0,31)));!1===b.exportHiddenCells&&(U=a.find("tr, th, td").filter(":hidden"),ia=0<U.length);q=0;O=[];fa=ya(this);L+="<table><thead>";z=P(a);d(z).each(function(){var a=d(this),g=document.defaultView.getComputedStyle(a[0],null);w="";F(this,"th,td",q,z.length,function(a,c,d){if(null!==a){var e="";w+="<th";if(b.mso.styles.length){var f=document.defaultView.getComputedStyle(a,null),h;for(h in b.mso.styles){var k=b.mso.styles[h],m=J(f,k);""===
m&&(m=J(g,k));""!==m&&"0px none rgb(0, 0, 0)"!==m&&"rgba(0, 0, 0, 0)"!==m&&(e+=""===e?'style="':";",e+=k+":"+m)}}""!==e&&(w+=" "+e+'"');e=Y(a);0<e&&(w+=' colspan="'+e+'"');e=ja(a);0<e&&(w+=' rowspan="'+e+'"');w+=">"+E(a,c,d)+"</th>"}});0<w.length&&(L+="<tr>"+w+"</tr>");q++});L+="</thead><tbody>";t=Q(a);d(t).each(function(){var a=d(this),g=null,h=null;w="";F(this,"td,th",q,z.length+t.length,function(c,k,l){if(null!==c){var f=E(c,k,l),e="",m=d(c).attr("data-tableexport-msonumberformat");"undefined"===
typeof m&&"function"===typeof b.mso.onMsoNumberFormat&&(m=b.mso.onMsoNumberFormat(c,k,l));"undefined"!==typeof m&&""!==m&&(e="style=\"mso-number-format:'"+m+"'");if(b.mso.styles.length){g=document.defaultView.getComputedStyle(c,null);h=null;for(var n in b.mso.styles)k=b.mso.styles[n],m=J(g,k),""===m&&(null===h&&(h=document.defaultView.getComputedStyle(a[0],null)),m=J(h,k)),""!==m&&"0px none rgb(0, 0, 0)"!==m&&"rgba(0, 0, 0, 0)"!==m&&(e+=""===e?'style="':";",e+=k+":"+m)}w+="<td";""!==e&&(w+=" "+e+
'"');e=Y(c);0<e&&(w+=' colspan="'+e+'"');c=ja(c);0<c&&(w+=' rowspan="'+c+'"');"string"===typeof f&&""!==f&&(f=gb(f),f=f.replace(/\n/g,"<br>"));w+=">"+f+"</td>"}});0<w.length&&(L+="<tr>"+w+"</tr>");q++});b.displayTableName&&(L+="<tr><td></td></tr><tr><td></td></tr><tr><td>"+E(d("<p>"+b.tableName+"</p>"))+"</td></tr>");L+="</tbody></table>"});var n='<html xmlns:o="urn:schemas-microsoft-com:office:office" '+Eb+' xmlns="http://www.w3.org/TR/REC-html40">';n+="<head>";n+='<meta http-equiv="content-type" content="application/vnd.ms-'+
ua+'; charset=UTF-8">';"excel"===ua&&(n+="\x3c!--[if gte mso 9]>",n+="<xml>",n+="<x:ExcelWorkbook>",n+="<x:ExcelWorksheets>",n+="<x:ExcelWorksheet>",n+="<x:Name>",n+=va,n+="</x:Name>",n+="<x:WorksheetOptions>",n+="<x:DisplayGridlines/>",b.mso.rtl&&(n+="<x:DisplayRightToLeft/>"),n+="</x:WorksheetOptions>",n+="</x:ExcelWorksheet>",n+="</x:ExcelWorksheets>",n+="</x:ExcelWorkbook>",n+="</xml>",n+="<![endif]--\x3e");n+="<style>";n+="@page { size:"+b.mso.pageOrientation+"; mso-page-orientation:"+b.mso.pageOrientation+
"; }";n+="@page Section1 {size:"+W[b.mso.pageFormat][0]+"pt "+W[b.mso.pageFormat][1]+"pt";n+="; margin:1.0in 1.25in 1.0in 1.25in;mso-header-margin:.5in;mso-footer-margin:.5in;mso-paper-source:0;}";n+="div.Section1 {page:Section1;}";n+="@page Section2 {size:"+W[b.mso.pageFormat][1]+"pt "+W[b.mso.pageFormat][0]+"pt";n+=";mso-page-orientation:"+b.mso.pageOrientation+";margin:1.25in 1.0in 1.25in 1.0in;mso-header-margin:.5in;mso-footer-margin:.5in;mso-paper-source:0;}";n+="div.Section2 {page:Section2;}";
n+="br {mso-data-placement:same-cell;}";n+="</style>";n+="</head>";n+="<body>";n+='<div class="Section'+("landscape"===b.mso.pageOrientation?"2":"1")+'">';n+=L;n+="</div>";n+="</body>";n+="</html>";if("string"===b.outputMode)return n;if("base64"===b.outputMode)return R(n);S(n,b.fileName+"."+Db,"application/vnd.ms-"+ua,"","base64",!1)}else if("png"===b.type)html2canvas(d(x)[0]).then(function(a){a=a.toDataURL();for(var c=atob(a.substring(22)),d=new ArrayBuffer(c.length),h=new Uint8Array(d),e=0;e<c.length;e++)h[e]=
c.charCodeAt(e);if("string"===b.outputMode)return c;if("base64"===b.outputMode)return R(a);"window"===b.outputMode?window.open(a):S(d,b.fileName+".png","image/png","","",!1)});else if("pdf"===b.type)if(!0===b.pdfmake.enabled){var oa={content:[]};d.extend(!0,oa,b.pdfmake.docDefinition);O=[];d(x).filter(function(){return N(d(this))}).each(function(a){var c=d(this),g=[],h="*",e=[];q=0;"string"!==typeof b.pdfmake.widths||"*"!==b.pdfmake.widths.trim()&&"auto"!==b.pdfmake.widths.trim()?Array.isArray(b.pdfmake.widths)&&
(g=b.pdfmake.widths):h=b.pdfmake.widths.trim();var k=function(a,b,c){var f=0;d(a).each(function(){var a=[];F(this,b,q,c,function(c,d,f){if("undefined"!==typeof c&&null!==c){var e=Ma(c),g=function(a){a=Math.min(255,Math.max(0,a)).toString(16);return 1===a.length?"0"+a:a};c={text:E(c,d,f)||" ",alignment:e.style.align,backgroundColor:"#"+g(e.style.bcolor[0])+g(e.style.bcolor[1])+g(e.style.bcolor[2]),color:"#"+g(e.style.color[0])+g(e.style.color[1])+g(e.style.color[2])};e.style.fstyle.includes("italic")&&
(c.fontStyle="italic");e.style.fstyle.includes("bold")&&(c.bold=!0);if(1<e.colspan||1<e.rowspan)c.colSpan=e.colspan||1,c.rowSpan=e.rowspan||1}else c={text:" "};0<=b.indexOf("th")&&(c.style="header");a.push(c)});a.length&&e.push(a);f<a.length&&(f=a.length);q++});return f};z=P(c);var l=k(z,"th,td",z.length);t=Q(c);c=k(t,"td",z.length+t.length);l=l>c?l:c;for(c=g.length;c<l;c++)g.push(h);oa.content.push({table:{headerRows:z.length?z.length:null,widths:g,body:e},layout:{layout:"noBorders",hLineStyle:function(a,
b){return 0},vLineWidth:function(a,b){return 0},hLineColor:function(a,c){return a<c.table.headerRows?b.pdfmake.docDefinition.styles.header.background:b.pdfmake.docDefinition.styles.alternateRow.fillColor},vLineColor:function(a,c){return a<c.table.headerRows?b.pdfmake.docDefinition.styles.header.background:b.pdfmake.docDefinition.styles.alternateRow.fillColor},fillColor:function(a,c,d){return 0===a%2?b.pdfmake.docDefinition.styles.alternateRow.fillColor:null}},pageBreak:0<a?"before":void 0})});"undefined"!==
typeof pdfMake&&"undefined"!==typeof pdfMake.createPdf&&(pdfMake.fonts={Roboto:{normal:"Roboto-Regular.ttf",bold:"Roboto-Medium.ttf",italics:"Roboto-Italic.ttf",bolditalics:"Roboto-MediumItalic.ttf"}},pdfMake.vfs.hasOwnProperty("Mirza-Regular.ttf")?(oa.defaultStyle.font="Mirza",d.extend(!0,pdfMake.fonts,{Mirza:{normal:"Mirza-Regular.ttf",bold:"Mirza-Bold.ttf",italics:"Mirza-Medium.ttf",bolditalics:"Mirza-SemiBold.ttf"}})):pdfMake.vfs.hasOwnProperty("gbsn00lp.ttf")?(oa.defaultStyle.font="gbsn00lp",
d.extend(!0,pdfMake.fonts,{gbsn00lp:{normal:"gbsn00lp.ttf",bold:"gbsn00lp.ttf",italics:"gbsn00lp.ttf",bolditalics:"gbsn00lp.ttf"}})):pdfMake.vfs.hasOwnProperty("ZCOOLXiaoWei-Regular.ttf")&&(oa.defaultStyle.font="ZCOOLXiaoWei",d.extend(!0,pdfMake.fonts,{ZCOOLXiaoWei:{normal:"ZCOOLXiaoWei-Regular.ttf",bold:"ZCOOLXiaoWei-Regular.ttf",italics:"ZCOOLXiaoWei-Regular.ttf",bolditalics:"ZCOOLXiaoWei-Regular.ttf"}})),d.extend(!0,pdfMake.fonts,b.pdfmake.fonts),pdfMake.createPdf(oa).getBuffer(function(a){S(a,
b.fileName+".pdf","application/pdf","","",!1)}))}else if(!1===b.jspdf.autotable){var Xa=new jspdf.jsPDF({orientation:b.jspdf.orientation,unit:b.jspdf.unit,format:b.jspdf.format});Xa.html(x[0],{callback:function(){$a(Xa,!1)},html2canvas:{scale:(Xa.internal.pageSize.width-2*b.jspdf.margins.left)/x[0].scrollWidth},x:b.jspdf.margins.left,y:b.jspdf.margins.top})}else{var k=b.jspdf.autotable.tableExport;if("string"===typeof b.jspdf.format&&"bestfit"===b.jspdf.format.toLowerCase()){var Ga="",wa="",mb=0;
d(x).each(function(){if(N(d(this))){var a=eb(d(this).get(0),"width","pt");if(a>mb){a>W.a0[0]&&(Ga="a0",wa="l");for(var b in W)W.hasOwnProperty(b)&&W[b][1]>a&&(Ga=b,wa="l",W[b][0]>a&&(wa="p"));mb=a}}});b.jspdf.format=""===Ga?"a4":Ga;b.jspdf.orientation=""===wa?"w":wa}if(null==k.doc&&(k.doc=new jspdf.jsPDF(b.jspdf.orientation,b.jspdf.unit,b.jspdf.format),k.wScaleFactor=1,k.hScaleFactor=1,"function"===typeof b.jspdf.onDocCreated))b.jspdf.onDocCreated(k.doc);Ca.fontName=k.doc.getFont().fontName;!0===
k.outputImages&&(k.images={});"undefined"!==typeof k.images&&(d(x).filter(function(){return N(d(this))}).each(function(){var a=0;O=[];!1===b.exportHiddenCells&&(U=d(this).find("tr, th, td").filter(":hidden"),ia=0<U.length);z=P(d(this));t=Q(d(this));d(t).each(function(){F(this,"td,th",z.length+a,z.length+t.length,function(a){bb(a,d(a).children(),k)});a++})}),z=[],t=[]);pb(k,function(){d(x).filter(function(){return N(d(this))}).each(function(){var a;q=0;O=[];!1===b.exportHiddenCells&&(U=d(this).find("tr, th, td").filter(":hidden"),
ia=0<U.length);fa=ya(this);k.columns=[];k.rows=[];k.teCells={};if("function"===typeof k.onTable&&!1===k.onTable(d(this),b))return!0;b.jspdf.autotable.tableExport=null;var c=d.extend(!0,{},b.jspdf.autotable);b.jspdf.autotable.tableExport=k;c.margin={};d.extend(!0,c.margin,b.jspdf.margins);c.tableExport=k;"function"!==typeof c.createdHeaderCell&&(c.createdHeaderCell=function(a,b){if("undefined"!==typeof k.columns[b.column.dataKey]){var d=k.columns[b.column.dataKey];if("undefined"!==typeof d.rect){a.contentWidth=
d.rect.width;if("undefined"===typeof k.heightRatio||0===k.heightRatio){var f=b.row.raw[b.column.dataKey].rowspan?b.row.raw[b.column.dataKey].rect.height/b.row.raw[b.column.dataKey].rowspan:b.row.raw[b.column.dataKey].rect.height;k.heightRatio=a.styles.rowHeight/f}f=b.row.raw[b.column.dataKey].rect.height*k.heightRatio;f>a.styles.rowHeight&&(a.styles.rowHeight=f)}a.styles.halign="inherit"===c.headerStyles.halign?"center":c.headerStyles.halign;a.styles.valign=c.headerStyles.valign;"undefined"!==typeof d.style&&
!0!==d.style.hidden&&("inherit"===c.headerStyles.halign&&(a.styles.halign=d.style.align),"inherit"===c.styles.fillColor&&(a.styles.fillColor=d.style.bcolor),"inherit"===c.styles.textColor&&(a.styles.textColor=d.style.color),"inherit"===c.styles.fontStyle&&(a.styles.fontStyle=d.style.fstyle))}});"function"!==typeof c.createdCell&&(c.createdCell=function(a,b){b=k.teCells[b.row.index+":"+b.column.dataKey];a.styles.halign="inherit"===c.styles.halign?"center":c.styles.halign;a.styles.valign=c.styles.valign;
"undefined"!==typeof b&&"undefined"!==typeof b.style&&!0!==b.style.hidden&&("inherit"===c.styles.halign&&(a.styles.halign=b.style.align),"inherit"===c.styles.fillColor&&(a.styles.fillColor=b.style.bcolor),"inherit"===c.styles.textColor&&(a.styles.textColor=b.style.color),"inherit"===c.styles.fontStyle&&(a.styles.fontStyle=b.style.fstyle))});"function"!==typeof c.drawHeaderCell&&(c.drawHeaderCell=function(a,b){var c=k.columns[b.column.dataKey];return(!0!==c.style.hasOwnProperty("hidden")||!0!==c.style.hidden)&&
0<=c.rowIndex?ab(a,b,c):!1});"function"!==typeof c.drawCell&&(c.drawCell=function(a,b){var c=k.teCells[b.row.index+":"+b.column.dataKey];if(!0!==("undefined"!==typeof c&&c.isCanvas))ab(a,b,c)&&(k.doc.rect(a.x,a.y,a.width,a.height,a.styles.fillStyle),"undefined"===typeof c||"undefined"!==typeof c.hasUserDefText&&!0===c.hasUserDefText||"undefined"===typeof c.elements||!c.elements.length?fb(a,{},k):(b=a.height/c.rect.height,b>k.hScaleFactor&&(k.hScaleFactor=b),k.wScaleFactor=a.width/c.rect.width,b=a.textPos.y,
db(a,c.elements,k),a.textPos.y=b,fb(a,c.elements,k)));else{c=c.elements[0];var e=d(c).attr("data-tableexport-canvas"),f=c.getBoundingClientRect();a.width=f.width*k.wScaleFactor;a.height=f.height*k.hScaleFactor;b.row.height=a.height;Za(a,c,e,k)}return!1});k.headerrows=[];z=P(d(this));d(z).each(function(){a=0;k.headerrows[q]=[];F(this,"th,td",q,z.length,function(b,c,d){var e=Ma(b);e.title=E(b,c,d);e.key=a++;e.rowIndex=q;k.headerrows[q].push(e)});q++});if(0<q)for(var g=q-1;0<=g;)d.each(k.headerrows[g],
function(){var a=this;0<g&&null===this.rect&&(a=k.headerrows[g-1][this.key]);null!==a&&0<=a.rowIndex&&(!0!==a.style.hasOwnProperty("hidden")||!0!==a.style.hidden)&&k.columns.push(a)}),g=0<k.columns.length?-1:g-1;var h=0;t=[];t=Q(d(this));d(t).each(function(){var b=[];a=0;F(this,"td,th",q,z.length+t.length,function(c,e,g){if("undefined"===typeof k.columns[a]){var f={title:"",key:a,style:{hidden:!0}};k.columns.push(f)}b.push(E(c,e,g));"undefined"!==typeof c&&null!==c?(f=Ma(c),f.isCanvas=c.hasAttribute("data-tableexport-canvas"),
f.elements=f.isCanvas?d(c):d(c).children(),"undefined"!==typeof d(c).data("teUserDefText")&&(f.hasUserDefText=!0)):(f=d.extend(!0,{},k.teCells[h+":"+(a-1)]),f.colspan=-1);k.teCells[h+":"+a++]=f});b.length&&(k.rows.push(b),h++);q++});if("function"===typeof k.onBeforeAutotable)k.onBeforeAutotable(d(this),k.columns,k.rows,c);ub(c.tableExport.doc,k.columns,k.rows,c);if("function"===typeof k.onAfterAutotable)k.onAfterAutotable(d(this),c);var e=b.jspdf.autotable;var m="undefined"===typeof I||"undefined"===
typeof I.y?0:I.y;e.startY=m+c.margin.top});$a(k.doc,"undefined"!==typeof k.images&&!1===jQuery.isEmptyObject(k.images));"undefined"!==typeof k.headerrows&&(k.headerrows.length=0);"undefined"!==typeof k.columns&&(k.columns.length=0);"undefined"!==typeof k.rows&&(k.rows.length=0);delete k.doc;k.doc=null})}var y,I,m,Pa,r;if("function"===typeof b.onTableExportEnd)b.onTableExportEnd();return this};var u=function(){return function(){this.contentWidth=this.y=this.x=this.width=this.height=0;this.rows=[];
this.columns=[];this.headerRow=null;this.settings={}}}(),D=function(){return function(d){this.raw=d||{};this.index=0;this.styles={};this.cells={};this.y=this.height=0}}(),K=function(){return function(d){this.raw=d;this.styles={};this.text="";this.contentWidth=0;this.textPos={};this.y=this.x=this.width=this.height=0}}(),xa=function(){return function(d){this.dataKey=d;this.options={};this.styles={};this.x=this.width=this.contentWidth=0}}()})(jQuery);
