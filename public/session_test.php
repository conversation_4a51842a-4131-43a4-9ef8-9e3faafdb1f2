<?php
// Session测试

// 定义应用目录
define('APP_PATH', __DIR__ . '/../application/');

try {
    // 加载框架引导文件
    require __DIR__ . '/../thinkphp/base.php';

    // 绑定到admin模块
    \think\Route::bind('admin');

    // 关闭路由
    \think\App::route(false);

    // 设置根url
    \think\Url::root('');

    echo "=== Session测试 ===\n";
    
    // 检查session配置
    $sessionConfig = \think\Config::get('session');
    echo "Session配置: " . print_r($sessionConfig, true) . "\n";
    
    // 检查cookie配置
    $cookieConfig = \think\Config::get('cookie');
    echo "Cookie配置: " . print_r($cookieConfig, true) . "\n";
    
    // 检查当前session状态
    echo "Session ID: " . session_id() . "\n";
    echo "Session状态: " . (session_status() === PHP_SESSION_ACTIVE ? '活跃' : '未活跃') . "\n";
    
    // 检查ThinkPHP的session
    echo "ThinkPHP Session前缀: " . \think\Session::getPrefix() . "\n";
    
    // 检查登录状态
    $auth = \app\admin\library\Auth::instance();
    echo "是否已登录: " . ($auth->isLogin() ? '是' : '否') . "\n";
    
    if ($auth->isLogin()) {
        echo "用户ID: " . $auth->id . "\n";
        echo "用户名: " . $auth->username . "\n";
    } else {
        echo "未登录原因: 检查session数据\n";
        
        // 显示所有session数据
        $sessionData = $_SESSION ?? [];
        echo "原生Session数据: " . print_r($sessionData, true) . "\n";
        
        // 显示ThinkPHP session数据
        $thinkSessionData = [];
        $prefix = \think\Session::getPrefix();
        foreach ($_SESSION as $key => $value) {
            if (strpos($key, $prefix) === 0) {
                $thinkSessionData[$key] = $value;
            }
        }
        echo "ThinkPHP Session数据: " . print_r($thinkSessionData, true) . "\n";
    }
    
    // 检查cookie
    echo "\n=== Cookie信息 ===\n";
    echo "所有Cookie: " . print_r($_COOKIE, true) . "\n";

} catch (Exception $e) {
    echo "Exception: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "\n";
}
?>
