<?php
// 简单的诊断脚本
echo "=== PHP诊断信息 ===\n";
echo "PHP版本: " . PHP_VERSION . "\n";
echo "当前时间: " . date('Y-m-d H:i:s') . "\n";
echo "脚本路径: " . __FILE__ . "\n";
echo "文档根目录: " . $_SERVER['DOCUMENT_ROOT'] . "\n";

echo "\n=== 函数检查 ===\n";
echo "putenv函数: " . (function_exists('putenv') ? '可用' : '不可用') . "\n";
echo "file_exists函数: " . (function_exists('file_exists') ? '可用' : '不可用') . "\n";

echo "\n=== 文件检查 ===\n";
$app_path = __DIR__ . '/../application/';
echo "APP_PATH: " . $app_path . "\n";
echo "APP_PATH存在: " . (is_dir($app_path) ? '是' : '否') . "\n";

$lock_file = $app_path . 'admin/command/Install/install.lock';
echo "安装锁文件: " . $lock_file . "\n";
echo "安装锁文件存在: " . (is_file($lock_file) ? '是' : '否') . "\n";

$base_file = __DIR__ . '/../thinkphp/base.php';
echo "base.php文件: " . $base_file . "\n";
echo "base.php存在: " . (is_file($base_file) ? '是' : '否') . "\n";

echo "\n=== 尝试加载ThinkPHP ===\n";
try {
    // 定义应用目录
    define('APP_PATH', __DIR__ . '/../application/');
    
    // 检查安装锁文件
    if (!is_file(APP_PATH . 'admin/command/Install/install.lock')) {
        echo "错误: 安装锁文件不存在\n";
        exit;
    }
    
    echo "开始加载base.php...\n";
    require __DIR__ . '/../thinkphp/base.php';
    echo "base.php加载成功!\n";
    
    echo "开始绑定admin模块...\n";
    \think\Route::bind('admin');
    echo "admin模块绑定成功!\n";
    
    echo "关闭路由...\n";
    \think\App::route(false);
    echo "路由关闭成功!\n";
    
    echo "设置根URL...\n";
    \think\Url::root('');
    echo "根URL设置成功!\n";
    
    echo "执行应用...\n";
    $response = \think\App::run();
    echo "应用执行成功!\n";
    echo "响应类型: " . get_class($response) . "\n";
    
} catch (Exception $e) {
    echo "Exception错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "Fatal Error: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
    echo "堆栈跟踪:\n" . $e->getTraceAsString() . "\n";
} catch (Throwable $e) {
    echo "Throwable错误: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}

echo "\n=== 诊断完成 ===\n";
